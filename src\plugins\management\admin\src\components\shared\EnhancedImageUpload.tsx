import React, { useState } from 'react';
import { Upload, message, Progress, Button } from 'antd';
import { UploadOutlined, ReloadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import styled from 'styled-components';

const UploadContainer = styled.div`
  .ant-upload-list {
    margin-top: 8px;
  }
  
  .upload-progress {
    margin-top: 8px;
  }
  
  .retry-button {
    margin-left: 8px;
  }
`;

const UploadButton = styled(Button)`
  height: 40px;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;

  &:hover {
    border-color: #667eea;
    background-color: #f0f2ff;
    color: #667eea;
  }

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
`;

interface EnhancedImageUploadProps {
  value?: UploadFile[];
  onChange?: (fileList: UploadFile[]) => void;
  maxCount?: number;
  accept?: string;
  disabled?: boolean;
  uploadText?: string;
  maxSize?: number; // in MB
  onUploadSuccess?: (file: UploadFile, response: any) => void;
  onUploadError?: (file: UploadFile, error: any) => void;
}

const EnhancedImageUpload: React.FC<EnhancedImageUploadProps> = ({
  value = [],
  onChange,
  maxCount = 1,
  accept = 'image/*',
  disabled = false,
  uploadText = 'Tải lên',
  maxSize = 10, // Default 10MB
  onUploadSuccess,
  onUploadError,
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  const [failedFile, setFailedFile] = useState<UploadFile | null>(null);

  const validateFile = (file: File): boolean => {
    // Check file type
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Chỉ được tải lên file hình ảnh!');
      return false;
    }

    // Check file size
    const isValidSize = file.size / 1024 / 1024 < maxSize;
    if (!isValidSize) {
      message.error(`Hình ảnh phải nhỏ hơn ${maxSize}MB!`);
      return false;
    }

    return true;
  };

  const uploadFile = async (file: UploadFile, retryAttempt = 0): Promise<void> => {
    if (!file.originFileObj) return;

    setUploading(true);
    setUploadProgress(0);
    setFailedFile(null);

    try {
      const formData = new FormData();
      formData.append('files', file.originFileObj);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Make the upload request
      const response = await fetch('/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('jwtToken') || ''}`,
        },
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result && result.length > 0) {
        setUploadProgress(100);
        
        // Update file with server response
        const updatedFile = {
          ...file,
          status: 'done' as const,
          response: result,
          url: result[0].url,
        };

        // Update file list
        const newFileList = [...value];
        const fileIndex = newFileList.findIndex(f => f.uid === file.uid);
        if (fileIndex >= 0) {
          newFileList[fileIndex] = updatedFile;
        } else {
          newFileList.push(updatedFile);
        }

        // Limit files if maxCount is set
        const limitedFiles = maxCount ? newFileList.slice(-maxCount) : newFileList;
        onChange?.(limitedFiles);
        
        onUploadSuccess?.(updatedFile, result);
        message.success('Tải lên thành công!');
        setRetryCount(0);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Upload error:', error);
      
      // Mark file as failed
      const failedFileObj = {
        ...file,
        status: 'error' as const,
        error: error,
      };
      
      setFailedFile(failedFileObj);
      onUploadError?.(failedFileObj, error);
      
      // Show retry option for network errors
      if (retryAttempt < 2) {
        message.error(`Tải lên thất bại. Thử lại lần ${retryAttempt + 1}/3`);
      } else {
        const errorMsg = (error as any)?.message || 'Không thể tải lên hình ảnh';
        message.error(`Lỗi upload: ${errorMsg}`);
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleRetry = () => {
    if (failedFile && retryCount < 2) {
      setRetryCount(prev => prev + 1);
      uploadFile(failedFile, retryCount + 1);
    }
  };

  const handleUploadChange: UploadProps['onChange'] = (info) => {
    const { file, fileList } = info;
    
    if (file.status === 'uploading') {
      return;
    }
    
    // Handle file selection (before upload)
    if (file.originFileObj && !file.status) {
      if (validateFile(file.originFileObj)) {
        uploadFile(file);
      }
    }
  };

  const handleRemove = (fileToRemove: UploadFile) => {
    const newFileList = value.filter((file) => file.uid !== fileToRemove.uid);
    onChange?.(newFileList);
    setFailedFile(null);
  };

  const fileList = value || [];
  const showUploadButton = !maxCount || fileList.length < maxCount;

  return (
    <UploadContainer>
      {/* File list */}
      {fileList.length > 0 && (
        <Upload
          listType="picture-card"
          fileList={fileList}
          onRemove={handleRemove}
          showUploadList={{
            showPreviewIcon: true,
            showRemoveIcon: !disabled,
          }}
        />
      )}

      {/* Upload progress */}
      {uploading && (
        <div className="upload-progress">
          <Progress 
            percent={uploadProgress} 
            status={uploadProgress === 100 ? 'success' : 'active'}
            strokeColor="#667eea"
          />
        </div>
      )}

      {/* Retry button for failed uploads */}
      {failedFile && retryCount < 2 && (
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRetry}
          className="retry-button"
          size="small"
        >
          Thử lại ({retryCount + 1}/3)
        </Button>
      )}

      {/* Upload button */}
      {showUploadButton && !disabled && !uploading && (
        <Upload
          listType="picture-card"
          fileList={[]}
          onChange={handleUploadChange}
          beforeUpload={() => false} // Prevent auto upload
          accept={accept}
          multiple={maxCount > 1}
          showUploadList={false}
          disabled={disabled}
        >
          <UploadButton icon={<UploadOutlined />} disabled={disabled} block>
            {uploadText}
          </UploadButton>
        </Upload>
      )}
    </UploadContainer>
  );
};

export default EnhancedImageUpload;
