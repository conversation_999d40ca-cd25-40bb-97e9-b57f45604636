export default ({ env }) => ({
  management: {
    enabled: true,
    resolve: "./src/plugins/management",
  },
  documentation: {
    enabled: false,
  },
  upload: {
    config: {
      sizeLimit: env.int("UPLOAD_SIZE_LIMIT", 50) * 1024 * 1024, // Default 50MB, configurable via env
      breakpoints: {
        xlarge: 1920,
        large: 1000,
        medium: 750,
        small: 500,
        xsmall: 64,
      },
      actionOptions: {
        upload: {},
        uploadStream: {},
      },
    },
  },
});
