# C<PERSON><PERSON> hình Upload trong Strapi

## Vấn đề hiện tại

Hệ thống upload ảnh trong plugin management đang gặp vấn đề không ổn định do:

1. **Giới hạn kích thước file không nhất quán**
2. **Thiếu cấu hình upload plugin riêng**
3. **<PERSON><PERSON> lý lỗi chưa tối ưu**
4. **Không có retry mechanism**

## Giải pháp đã triển khai

### 1. <PERSON><PERSON><PERSON> hình Upload Plugin

**File: `config/plugins.ts`**
```typescript
upload: {
  config: {
    sizeLimit: env.int("UPLOAD_SIZE_LIMIT", 50) * 1024 * 1024, // Default 50MB
    breakpoints: {
      xlarge: 1920,
      large: 1000,
      medium: 750,
      small: 500,
      xsmall: 64,
    },
    actionOptions: {
      upload: {},
      uploadStream: {},
    },
  },
}
```

### 2. <PERSON><PERSON><PERSON> hình Middleware

**File: `config/middlewares.ts`**
```typescript
{
  name: "strapi::body",
  config: {
    formLimit: "256mb",
    jsonLimit: "256mb", 
    textLimit: "256mb",
    formidable: {
      maxFileSize: 200 * 1024 * 1024, // 200MB
    },
  },
}
```

### 3. Giới hạn nhất quán

- **Middleware**: 200MB (giới hạn tối đa)
- **Upload Plugin**: 50MB (có thể cấu hình qua env)
- **Frontend Components**: 10MB (giới hạn thực tế cho user)

## Biến môi trường

Thêm vào file `.env`:

```bash
# Upload configuration
UPLOAD_SIZE_LIMIT=50  # Size limit in MB for upload plugin
```

## Cách tăng giới hạn upload

### 1. Tăng giới hạn qua biến môi trường

```bash
# Trong file .env
UPLOAD_SIZE_LIMIT=100  # Tăng lên 100MB
```

### 2. Tăng giới hạn middleware (nếu cần)

```typescript
// config/middlewares.ts
formidable: {
  maxFileSize: 500 * 1024 * 1024, // Tăng lên 500MB
}
```

### 3. Tăng giới hạn frontend components

```typescript
// Trong các component upload
maxSize = 20 // Tăng lên 20MB
```

## Component Upload cải tiến

### EnhancedImageUpload

Tính năng mới:
- **Progress tracking**: Hiển thị tiến trình upload
- **Retry mechanism**: Tự động thử lại khi thất bại
- **Better error handling**: Xử lý lỗi chi tiết hơn
- **Validation**: Kiểm tra file type và size

Sử dụng:
```tsx
import EnhancedImageUpload from '../shared/EnhancedImageUpload';

<EnhancedImageUpload
  value={uploadedImages}
  onChange={setUploadedImages}
  maxCount={10}
  maxSize={10} // 10MB
  onUploadSuccess={(file, response) => {
    console.log('Upload success:', file, response);
  }}
  onUploadError={(file, error) => {
    console.error('Upload error:', file, error);
  }}
/>
```

## Troubleshooting

### 1. Upload thất bại liên tục

**Nguyên nhân có thể:**
- File quá lớn
- Kết nối mạng không ổn định
- Server overload
- Thiếu quyền truy cập

**Giải pháp:**
- Kiểm tra kích thước file
- Sử dụng EnhancedImageUpload với retry
- Kiểm tra logs server
- Kiểm tra token authentication

### 2. Upload chậm

**Nguyên nhân:**
- File size lớn
- Bandwidth thấp
- Server processing chậm

**Giải pháp:**
- Compress ảnh trước khi upload
- Tăng timeout
- Optimize server performance

### 3. Memory issues

**Nguyên nhân:**
- Upload nhiều file lớn cùng lúc
- Server memory không đủ

**Giải pháp:**
- Giảm maxCount
- Upload từng file một
- Tăng server memory

## Monitoring

### 1. Logs để theo dõi

```bash
# Server logs
tail -f logs/strapi.log | grep upload

# Error logs
tail -f logs/error.log | grep upload
```

### 2. Metrics quan trọng

- Upload success rate
- Average upload time
- File size distribution
- Error frequency

## Best Practices

1. **Luôn validate file trước khi upload**
2. **Sử dụng progress indicator cho UX tốt hơn**
3. **Implement retry mechanism cho network issues**
4. **Log errors để debug**
5. **Set reasonable size limits**
6. **Compress images khi có thể**
7. **Use CDN cho static files**

## Cấu hình Production

```bash
# .env.production
UPLOAD_SIZE_LIMIT=20  # Giảm xuống cho production
NODE_OPTIONS="--max-old-space-size=4096"  # Tăng memory cho Node.js
```

```typescript
// config/middlewares.ts (production)
formidable: {
  maxFileSize: 50 * 1024 * 1024, // 50MB cho production
  maxFields: 1000,
  maxFieldsSize: 20 * 1024 * 1024,
}
```
